<template>
    <div class="borrowing-application">
        <n-search-table-page
            ref="searchTablePageRef"
            :data-table-props="{
                columns,
                size: 'small',
                scrollX: 1600,
                maxHeight: 'calc(100vh - 440px)'
            }"
            :params="params"
            :data-api="$apis.nebula.api.v1.documentLibrary.loans.list"
            :search-props="{
                showAdd: store.permissions.indexOf('borrowingApplicationAdd') > -1,
                showInput: false,
                searchInputPlaceholder: '请输入文件编号、名称 / 原文件编号',
                inputWidth: '280px'
            }"
            :search-table-space="{
                size: 20
            }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            @add="handleModel(null)"
            @reset="handleReset"
        >
            <template #search_form_middle>
                <n-input class="w-198px" v-model:value="params.documentNo" placeholder="请输入文件编号" />
                <n-input class="w-198px" v-model:value="params.documentName" placeholder="请输入文件名称" />
            </template>
            <template #search_form_after>
                <n-button type="primary" @click="show = !show">{{ show ? '收起' : '更多' }}</n-button>
            </template>
            <template #search_bottom_layout>
                <n-collapse-transition v-if="show" :show="show">
                    <n-space>
                        <n-select
                            class="w-178px"
                            v-model:value="params.documentModuleType"
                            :options="fileTypeOptions"
                            clearable
                            placeholder="文件类型"
                        />
                        <select-tree-dictionary
                            class="w-178px!"
                            v-model:value="params.documentCategoryId"
                            :params="params.documentModuleType === 2 ? 'internal' : 'external'"
                            placeholder="选择文件类别"
                            multiple
                            style="width: 200px"
                            checkable
                            filterable
                            clearable
                            cascade
                            :show-path="false"
                        />
                        <n-select
                            class="w-128px"
                            v-model:value="params.approvalStatus"
                            :options="($datas.borrowing.status as any)"
                            clearable
                            placeholder="状态"
                        />
                        <n-input class="w-178px" v-model:value="params.userNickname" placeholder="申请人" />
                    </n-space>
                </n-collapse-transition>
            </template>
            <template #search_handle_after>
                <n-permission has="borrowingApplicationExport">
                    <n-button type="warning" @click="handleExport">导出</n-button>
                </n-permission>
            </template>

            <template #table_borrowPeriod="{ row }">
                <n-time :time="row.borrowTime" format="yyyy-MM-dd" />
                至
                <n-time :time="row.dueTime" format="yyyy-MM-dd" />
            </template>
            <template #table_approvalApplyTime="{ row }">
                <n-time :time="row.approvalApplyTime" format="yyyy-MM-dd" />
            </template>
            <template #table_auditors="{ row }">
                <n-space vertical>
                    <span v-for="item in row.auditors" :key="item.id" size="small">{{ item }}</span>
                </n-space>
            </template>
            <template #table_approvers="{ row }">
                <n-space vertical>
                    <span v-for="item in row.approvers" :key="item.id" size="small">{{ item }}</span>
                </n-space>
            </template>
            <template #table_approvalStatus="{ row }">
                <n-tag
                    :type="$datas.borrowing.status[row.approvalStatus !== -1 ? row.approvalStatus : 0]?.type"
                    round
                    :bordered="false"
                    size="small"
                >
                    {{ $datas.borrowing.status[row.approvalStatus !== -1 ? row.approvalStatus : 0]?.label }}
                </n-tag>
            </template>
            <template #table_todo="{ row }">
                <n-space justify="start" :wrap="false" class="px-10px">
                    <n-permission has="borrowingApplicationDetail">
                        <n-button size="tiny" type="primary" @click="handleModel(row, true)">借阅详情</n-button>
                    </n-permission>
                    <n-dropdown
                        v-if="todoOptions(row).length > 0"
                        trigger="click"
                        :options="todoOptions(row)"
                        @select="(key) => handleTodoMenu(key, row)"
                    >
                        <n-button size="tiny">更多</n-button>
                    </n-dropdown>
                </n-space>
            </template>
        </n-search-table-page>
    </div>
</template>

<script setup lang="ts">
import { type DataTableColumns } from 'naive-ui';
import useStore from '@/store/modules/main';

const store = useStore();

const searchTablePageRef = ref();
const show = ref(false);
const params = ref<any>({
    documentNo: '', // 文件编号
    documentName: '', // 文件名称
    documentModuleType: null, // 文件模块，1：书籍 ，2：内部文档 ， 3：外部文档
    documentCategoryId: [], // 文件类别
    approvalStatus: null, // 状态
    userNickname: '' // 申请人
});

// 文件类型
const fileTypeOptions: any[] = [
    { label: '内部文件', value: 2 },
    { label: '外部文件', value: 3 }
];

const columns: DataTableColumns = [
    {
        title: '序号',
        key: 'key',
        align: 'center',
        width: 60,
        fixed: 'left',
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    { title: '借阅文件数', key: 'borrowDocumentsCount', align: 'center', ellipsis: { tooltip: true } },
    { title: '交还文件数', key: 'recoverDocumentsCount', align: 'center', ellipsis: { tooltip: true } },
    { title: '借阅期限', key: 'borrowPeriod', align: 'center', width: 210, ellipsis: { tooltip: true } },
    { title: '申请人', key: 'userNickname', align: 'center', ellipsis: { tooltip: true } },
    { title: '申请日期', key: 'approvalApplyTime', align: 'center', width: 120 },
    { title: '审核人', key: 'auditors', align: 'center', width: 180, ellipsis: { tooltip: true } },
    { title: '批准人', key: 'approvers', align: 'center', width: 180, ellipsis: { tooltip: true } },
    { title: '回收人', key: 'recoverNames', align: 'center', ellipsis: { tooltip: true } },
    { title: '状态', key: 'approvalStatus', align: 'center', width: 80 },
    { title: '操作', key: 'todo', align: 'center', fixed: 'right', width: 140 }
];

const init = () => {
    nextTick(() => {
        searchTablePageRef.value?.initData();
    });
};

const handleReset = () => {
    params.value = {
        documentNo: '',
        documentName: '',
        documentModuleType: null,
        documentCategoryId: [],
        approvalStatus: null,
        userNickname: ''
    };
    init();
};

const handleExport = () => {
    window.$message.info('点击导出');
};

/**
 * 更多操作
 */
const todoOptions = computed(() => (row: any) => {
    const options = [];
    const perms = store.permissions;
    if (perms.includes('borrowingApplicationEdit') && [1, 4].includes(row.approvalStatus)) {
        options.push({ label: '编辑', key: 'edit' });
    }
    if (perms.includes('borrowingApplicationRevoke') && row.approvalStatus === 2) {
        options.push({ label: '撤销', key: 'revoke' });
    }
    if (perms.includes('borrowingApplicationRecycle') && row.approvalStatus === 3) {
        options.push({ label: '回收', key: 'recycle' });
    }
    if (perms.includes('borrowingApplicationDelete') && row.approvalStatus === 1) {
        options.push({ label: '删除', key: 'delete' });
    }
    return options;
});

const handleTodoMenu = (key: string, row: any) => {
    switch (key) {
        case 'edit':
            handleModel(row);
            break;
        case 'revoke':
            handleRevoke(row);
            break;
        case 'recycle':
            handleRecycle(row);
            break;
        case 'delete':
            handleDelete(row);
            break;
    }
};

/**
 * 新增、编辑、详情
 */
const handleModel = (row: any, isPreview = false) => {
    console.log('编辑', row);
    $alert.dialog({
        title: isPreview ? '详情' : row ? '编辑' : '新增',
        width: '60%',
        content: import('./models/borrow-form.vue'),
        props: {
            row,
            isPreview,
            onSave: () => init(),
            onTemp: () => init()
        }
    });
};

/**
 * 撤销操作
 */
const handleRevoke = (row: any) => {
    window.$dialog.warning({
        title: '撤销',
        content: `确认后撤销当前流程，是否确认？`,
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
            await api.sass.api.v1.workflow.workflow.reject(row.workflowId as string);
            window.$message.success(`撤销成功`);
            init();
        }
    });
};

/**
 * 回收操作
 */
const handleRecycle = (row: any) => {
    console.log(row);
    $alert.dialog({
        title: '回收',
        width: '60%',
        content: import('./models/borrow-form.vue'),
        props: {
            row,
            isPreview: true,
            isRecycle: true,
            onSave: () => init()
        }
    });
};

/**
 * 删除操作
 */
const handleDelete = (row: any) => {
    window.$dialog.warning({
        title: '删除',
        content: `确认后删除当前流程，是否确认？`,
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
            await $apis.nebula.api.v1.documentLibrary.loans.delete(row.id);
            window.$message.success(`删除成功`);
            init();
        }
    });
};
</script>
